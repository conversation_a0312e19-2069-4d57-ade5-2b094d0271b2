"use client";

import Hero from '@/components/Heros/Hero';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import styles from "./page.module.scss";
import { useTranslation } from "@/hooks/useTranslation";

export default function QuestionsFrequentesClient({ params }) {
  const { t } = useTranslation('faq');
  const locale = params.locale || 'fr';

  // Récupération des questions depuis les locales
  const identityQuestions = t('questions.identity') || [];

  return (
    <div className={styles.main}>
      <Hero title={t('hero_title')} locale={locale} />

      <div className={styles.accordionBlock}>
        <h2 className='container'>{t('identity_section')}</h2>

        <Accordion>
          {identityQuestions.map((item, index) => (
            <AccordionItem key={index} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
