"use client";

import Hero from '@/components/Heros/Hero';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import styles from "./page.module.scss";
import { useTranslation } from "@/hooks/useTranslation";

export default function QuestionsFrequentesClient({ params }) {
  const { t } = useTranslation('faq');
  const locale = params.locale || 'fr';


  return (
    <div className={styles.main}>
      <Hero title={t('hero_title')} locale={locale} />

      <div className={styles.accordionBlock}>
        <h2 className='container'>{t('identity_section')}</h2>
        
        <Accordion>
          <AccordionItem title="Combien de temps faut-il pour concevoir un logo?">
            "Le délai de conception d'un logo personnalisé peut varier en fonction de la complexité du projet et de vos besoins spécifiques. En général, nous nous efforçons de livrer un logo en environ 4 à 6 semaines."
          </AccordionItem>
          <AccordionItem title="Question 2">
            {defaultContent}
          </AccordionItem>
          <AccordionItem title="Question 3">
            {defaultContent}
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
