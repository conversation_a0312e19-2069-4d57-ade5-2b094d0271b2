"use client";

import Hero from '@/components/Heros/Hero';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import styles from "./page.module.scss";
import { useTranslation } from "@/hooks/useTranslation";

export default function QuestionsFrequentesClient({ params }) {
  const { t } = useTranslation('faq');
  const locale = params.locale || 'fr';

  // Récupération des questions depuis les locales
  const identityQuestions = t('questions.identity') || [];
  const webQuestions = t('questions.web') || [];

  return (
    <div className={styles.main}>
      <Hero title={t('hero_title')} locale={locale} />

      {/* Section Identité visuelle */}
      <div className={`${styles.accordionBlock} container`}>
        <h2>{t('identity_section')}</h2>

        <Accordion>
          {identityQuestions.map((item, index) => (
            <AccordionItem key={`identity-${index}`} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      {/* Section Développement web */}
      <div className={`${styles.accordionBlock} container`}>
        <h2>{t('website_section')}</h2>

        <Accordion>
          {webQuestions.map((item, index) => (
            <AccordionItem key={`web-${index}`} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
